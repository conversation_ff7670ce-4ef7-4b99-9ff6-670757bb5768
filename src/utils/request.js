import axios from 'axios'
import {Message} from 'element-ui'
import store from '@/store'
import {_requestApi} from '@/request'
import apis from '@/api/reqConfig'
import router from '@/router'
import {getToken} from '@/utils/auth'

let afterRefreshRequestList = [] // 被挂起的请求数组
let isRefreshing = false // 是否有请求正在刷新token

/**
 * push所有请求到数组中
 * @param {function} cb 回调函数
 */
function subscribeTokenRefresh(cb) {
  afterRefreshRequestList.push(cb)
}

/**
 * 刷新请求
 * @param {string} token 新的token
 */
function onRrefreshed(token) {
  afterRefreshRequestList.map((cb) => cb(token))
}

/**
 * plugins 将指定的请求参数某个值拼接到请求地址url中且删除该请求参数
 * @param {object} config
 */
async function handleUrlVariable(config) {
  let { url, data } = config
  if (url && url.indexOf(':{') !== -1) {
    const arr = url.match(/:{.*?}/g)
    arr.forEach((item) => {
      const variable = item.match(/:{(\S*)}/)
      if (data[variable[1]]) {
        url = url.replace(item, data[variable[1]])
        delete data[variable[1]]
      }
    })
    return {
      ...config,
      url
    }
  } else {
    return config
  }
}

/**
 * 处理特定选项
 * @param {object} config
 */
function handleOptions(config) {
  if (store.getters.token) {
    config.headers['Authorization'] = `Bearer ${getToken()}`
    const randomStr = parseInt(Math.random().toFixed(2) * 9000 + 1000)
    config.url += `${
      config.url.indexOf('?') !== -1 ? '&' : '?'
    }sequence=${new Date().getTime()}${randomStr}`

    return config
  } else {
    return config
  }
}

// --------------------------------------------------------

// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  withCredentials: false, // 跨域请求时不发送Cookie
  timeout: 3600000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  (config) => {
    if (config.url.indexOf('common/upload/pdf/datasheet') > -1) {
      config.timeout = 60000
    }
    config = handleOptions(config)
    config = handleUrlVariable(config)
    return config
  },
  (error) => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  async(response) => {
    if (response.data && response.data.code === 200) {
      // 请求成功
      return response.data
    } else if (response.data && response.data.code === 201) {
      // 创建成功
      return response.data
    }else if (response.status === 204) {
      // 删除成功
        return { code: 200 }
    } else {
      // 其他错误情况
      let defaultErrorMessage = '请求失败'
      if (response.data && response.data.code) {
        if (response.data.code === 401 || response.data.code === 402 || response.data.code === 403) {
          defaultErrorMessage = ''
          await store.dispatch('user/resetToken') // 仅重置token，不走接口
          let redirect = window.location.hash
          if (redirect.indexOf('#/') === 0) {
            redirect = redirect.replace('#/', '')
          }
          router.push(
            `/login?redirect=${redirect}&error_code=${response.data.code}`
          )
        }
      }
      const withoutMessage = response.config.requestOptions.withoutErrorMessage // 不需要统一错误提示处理
      console.log(withoutMessage)
      if (!withoutMessage && defaultErrorMessage) {
        // defaultErrorMessage为空不报错 =》 针对4001和4004情况
        Message({
          message: response.data.message || defaultErrorMessage,
          type: 'error',
          duration: 5000
        })
        console.error(response)
      }
      return response.data
    }
  },
  (error) => {
    let msg = ''
    if (error && error.response) {
      switch (error.response.status) {
        case 204:
          // 204状态码表示没有内容，不作为错误处理
          return Promise.resolve({ code: 200 })
        case 400:
          msg = '错误的请求'
          break
        case 401:
          msg = '请重新登录'
          break
        case 403:
          msg = '拒绝访问'
          break
        case 404:
          msg = `请求地址出错`
          break
        case 500:
          msg = `服务器错误`
          break
        default:
      }
    }

    Message({
      message: msg || error,
      type: 'error',
      duration: 5000
    })
    return Promise.reject(error)
  }
)

export const requestApi = _requestApi(apis, service)
