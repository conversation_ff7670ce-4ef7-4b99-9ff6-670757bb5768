const base = ''
export default {
  // ====================== 订单管理接口 ======================
  // 获取订单详情
  getOrderDetail: {
    url: base + '/order/:{order_id}',
    method: 'GET'
  },

  // 获取订单列表
  getOrderList: {
    url: base + '/order/',
    method: 'GET'
  },

  // 获取订单项列表
  getOrderItems: {
    url: base + '/order/:{order_id}/items',
    method: 'GET'
  },

  // 创建订单
  createOrder: {
    url: base + '/order/',
    method: 'POST'
  },

  // 更新订单
  updateOrder: {
    url: base + '/order/:{order_id}',
    method: 'PUT'
  },

  // 删除订单
  deleteOrder: {
    url: base + '/order/:{order_id}',
    method: 'DELETE'
  },

  // 更新订单状态
  updateOrderStatus: {
    url: base + '/order/:{order_id}/status',
    method: 'PATCH'
  },

  // 支付订单
  payOrder: {
    url: base + '/order/:{order_id}/pay',
    method: 'PATCH'
  },

  // 取消订单
  cancelOrder: {
    url: base + '/order/:{order_id}/cancel',
    method: 'PATCH'
  },

  // 获取用户订单列表
  getUserOrders: {
    url: base + '/order/user/:{user_id}',
    method: 'GET'
  }
}
