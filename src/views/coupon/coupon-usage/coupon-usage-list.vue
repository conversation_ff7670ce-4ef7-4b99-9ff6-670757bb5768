<template>
  <div class="product-list">
    <h1>优惠券使用记录</h1>

    <!-- 头部菜单 -->
    <div class="search-bar">
      <div class="search-container">
        <div class="input-group">
          <el-input
            v-model="searchForm.username"
            placeholder="用户名/手机号"
            size="mini"
            class="filter-item"
          />
        </div>
        <div class="input-group">
          <el-input
            v-model="searchForm.coupon_name"
            placeholder="优惠券名称"
            size="mini"
            class="filter-item"
          />
        </div>
        <el-button type="primary" size="mini" @click="handleSearch"
          >查询
        </el-button>
        <el-button type="primary" size="mini" @click="resetSearch"
          >重置
        </el-button>
      </div>
      <div class="left-buttons">
        <el-button type="primary" size="mini" @click="distributeCoupon"
          >发放优惠券
        </el-button>
      </div>
    </div>
    <!-- 头部菜单结束 -->

    <!-- 表格 -->
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column prop="id" label="记录ID" width="80" align="center" />
      <el-table-column
        prop="username"
        label="用户"
        width="120"
        align="center"
      />
      <el-table-column prop="coupon_name" label="优惠券名称" align="center" />
      <el-table-column
          prop="distribution_channel"
          label="分发渠道"
          width="120"
          align="center"
      >
        <template slot-scope="scope">
          <el-tag :type="getChannelTagType(scope.row.distribution_channel)">
            {{ getChannelText(scope.row.distribution_channel) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="order_id"
          label="订单ID"
          width="100"
          align="center"
      >
        <template slot-scope="scope">
          <span v-if="!scope.row.order_id" class="unused-text">未使用</span>
          <el-button
            v-else
            type="text"
            class="order-id-link"
            @click="viewOrderDetail(scope.row.order_id)"
          >
            {{ scope.row.order_id }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        prop="discount_amount"
        label="优惠金额"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <span class="discount-amount"
            >¥{{ (scope.row.discount_amount || 0).toFixed(2) }}</span
          >
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.order_id ? 'success' : 'info'">
            {{ scope.row.order_id ? "已使用" : "未使用" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="used_at"
        label="使用时间"
        width="180"
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.row.used_at | formatDateTime }}
        </template>
      </el-table-column>
      <el-table-column
        prop="created_at"
        label="获取时间"
        width="180"
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.row.created_at | formatDateTime }}
        </template>
      </el-table-column>
    </el-table>
    <!-- 表格结束 -->

    <!-- 列表分页 -->
    <div class="pagination-container">
      <el-pagination
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :current-page.sync="pagination.page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 列表分页结束 -->

    <!-- 新增/编辑优惠券弹窗 -->
    <coupon-distribute
      :visible.sync="dialogFormVisible"
      :title="dialogTitle"
      :detail="formData"
      @submit="submitForm"
      @cancel="dialogFormVisible = false"
    />

    <!-- 订单详情弹窗 -->
    <el-dialog
      title="订单详情"
      :visible.sync="orderDetailVisible"
      width="700px"
      class="order-detail-dialog"
    >
      <div class="detail-content" v-if="currentOrder">
        <div class="order-info">
          <h4>订单信息</h4>
          <div class="order-items">
            <div class="order-item">
              <span class="item-label">订单ID:</span>
              <span class="item-value">{{ currentOrder.id }}</span>
            </div>
            <div class="order-item">
              <span class="item-label">订单号:</span>
              <span class="item-value">{{ currentOrder.order_no }}</span>
            </div>
            <div class="order-item">
              <span class="item-label">用户ID:</span>
              <span class="item-value">{{ currentOrder.user_id }}</span>
            </div>
            <div class="order-item">
              <span class="item-label">订单状态:</span>
              <span class="item-value">
                <el-tag :type="getOrderStatusType(currentOrder.status)">
                  {{ getOrderStatusText(currentOrder.status) }}
                </el-tag>
              </span>
            </div>
            <div class="order-item">
              <span class="item-label">支付状态:</span>
              <span class="item-value">
                <el-tag :type="getPaymentStatusType(currentOrder.payment_status)">
                  {{ getPaymentStatusText(currentOrder.payment_status) }}
                </el-tag>
              </span>
            </div>
            <div class="order-item">
              <span class="item-label">创建时间:</span>
              <span class="item-value">{{ currentOrder.created_at | formatDateTime }}</span>
            </div>
            <div class="order-item">
              <span class="item-label">更新时间:</span>
              <span class="item-value">{{ currentOrder.updated_at | formatDateTime }}</span>
            </div>
          </div>
        </div>

        <div class="order-total">
          <div class="total-item">
            <span class="total-label">订单总额:</span>
            <span class="total-value">¥{{ (currentOrder.total_amount || 0).toFixed(2) }}</span>
          </div>
          <div class="total-item">
            <span class="total-label">应付金额:</span>
            <span class="total-value">¥{{ (currentOrder.payable_amount || 0).toFixed(2) }}</span>
          </div>
          <div class="total-item final-total">
            <span class="total-label">实付金额:</span>
            <span class="total-value">¥{{ (currentOrder.actual_amount_paid || 0).toFixed(2) }}</span>
          </div>
        </div>

        <div class="order-items-section" v-if="orderItems && orderItems.length > 0">
          <h4>订单项详情</h4>
          <el-table :data="orderItems" border style="width: 100%">
            <el-table-column prop="product_name" label="产品名称" align="center" />
            <el-table-column prop="quantity" label="数量" width="80" align="center" />
            <el-table-column prop="unit_price" label="单价" width="100" align="center">
              <template slot-scope="scope">
                ¥{{ (scope.row.unit_price || 0).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="total_price" label="小计" width="100" align="center">
              <template slot-scope="scope">
                ¥{{ (scope.row.total_price || 0).toFixed(2) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="orderDetailVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";
import moment from "moment";
import CouponDistribute from "./coupon-distribute.vue";

export default {
  name: "CouponUsageList",
  components: {
    CouponDistribute,
  },
  filters: {
    formatDateTime(date) {
      if (!date) return "";
      return moment(date).format("YYYY-MM-DD HH:mm:ss");
    },
  },
  data() {
    return {
      searchForm: {
        username: "",
        coupon_name: "",
      },
      listLoading: false,
      tableData: [],
      currentPage: 1,
      pagination: {
        page: 1,
        pageSize: 10,
      },
      total: 0,
      dialogFormVisible: false,
      dialogTitle: "",
      formData: {},
      // 订单详情相关
      orderDetailVisible: false,
      currentOrder: null,
      orderItems: [],
      orderDetailLoading: false,
    };
  },
  created() {
    this.getCouponUsageList();
  },
  methods: {
    // 获取优惠券使用记录列表
    getCouponUsageList() {
      this.listLoading = true;

      const params = {
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
      };

      // 添加搜索参数
      if (this.searchForm.username) {
        params.username = this.searchForm.username;
      }

      if (this.searchForm.coupon_name) {
        params.coupon_name = this.searchForm.coupon_name;
      }

      // 打印请求参数便于调试
      console.log("请求参数:", params);

      requestApi({
        name: "getCouponUsagesBySearch",
        data: params,
      })
        .then((response) => {
          this.listLoading = false;
          console.log("API响应:", response);
          if (response && response.code === 200) {
            // 根据新的数据结构处理响应
            this.tableData = response.data || [];
            // 如果API没有返回total，使用数组长度作为总数
            this.total = response.total || this.tableData.length;
          } else {
            this.$message.error(response.message || "获取优惠券使用记录失败");
          }
        })
        .catch((error) => {
          this.listLoading = false;
          console.error("获取优惠券使用记录失败", error);
          this.$message.error("获取优惠券使用记录失败");
        });
    },

    handleSearch() {
      this.pagination.page = 1;
      this.getCouponUsageList();
    },

    resetSearch() {
      this.searchForm = {
        username: "",
        coupon_name: "",
      };
      this.getCouponUsageList();
    },

    // 分页处理
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.getCouponUsageList();
    },

    handleCurrentChange(val) {
      this.pagination.page = val;
      this.getCouponUsageList();
    },

    distributeCoupon() {
      this.dialogFormVisible = true;
      this.dialogTitle = "发放优惠券";
      this.formData = {};
    },

    submitForm(formData) {
      // 实现提交表单的逻辑
      console.log("提交表单:", formData);
      this.dialogFormVisible = false;
      this.getCouponUsageList();
    },

    // 获取分发渠道显示文本
    getChannelText(channel) {
      const channelMap = {
        new_user: "新人注册",
        view_activity: "浏览活动",
        share_activity: "分享活动",
        purchase: "销售购买",
        manual: "手动发放",
        null: "未知",
        undefined: "未知",
      };
      return channelMap[channel] || "未知";
    },

    // 获取分发渠道标签类型
    getChannelTagType(channel) {
      const typeMap = {
        new_user: "success",
        view_activity: "info",
        share_activity: "warning",
        purchase: "primary",
        manual: "danger",
        null: "info",
        undefined: "info",
      };
      return typeMap[channel] || "info";
    },

    // 查看订单详情
    viewOrderDetail(orderId) {
      if (!orderId) {
        this.$message.warning("订单ID不能为空");
        return;
      }

      this.orderDetailLoading = true;
      this.orderDetailVisible = true;

      // 获取订单详情
      requestApi({
        name: "getOrderDetail",
        data: { order_id: orderId },
      })
        .then((response) => {
          this.orderDetailLoading = false;
          if (response && response.code === 200) {
            this.currentOrder = response.data;
            // 获取订单项详情
            this.getOrderItems(orderId);
          } else {
            this.$message.error(response.message || "获取订单详情失败");
            this.orderDetailVisible = false;
          }
        })
        .catch((error) => {
          this.orderDetailLoading = false;
          console.error("获取订单详情失败", error);
          this.$message.error("获取订单详情失败");
          this.orderDetailVisible = false;
        });
    },

    // 获取订单项详情
    getOrderItems(orderId) {
      requestApi({
        name: "getOrderItems",
        data: { order_id: orderId },
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.orderItems = response.data || [];
          } else {
            console.warn("获取订单项失败:", response.message);
            this.orderItems = [];
          }
        })
        .catch((error) => {
          console.error("获取订单项失败", error);
          this.orderItems = [];
        });
    },

    // 获取订单状态显示文本
    getOrderStatusText(status) {
      const statusMap = {
        pending: "待处理",
        confirmed: "已确认",
        preparing: "准备中",
        ready: "已完成",
        delivered: "已送达",
        cancelled: "已取消",
        null: "未知",
        undefined: "未知",
      };
      return statusMap[status] || "未知";
    },

    // 获取订单状态标签类型
    getOrderStatusType(status) {
      const typeMap = {
        pending: "warning",
        confirmed: "primary",
        preparing: "info",
        ready: "success",
        delivered: "success",
        cancelled: "danger",
        null: "info",
        undefined: "info",
      };
      return typeMap[status] || "info";
    },

    // 获取支付状态显示文本
    getPaymentStatusText(status) {
      const statusMap = {
        pending: "待支付",
        paid: "已支付",
        failed: "支付失败",
        refunded: "已退款",
        null: "未知",
        undefined: "未知",
      };
      return statusMap[status] || "未知";
    },

    // 获取支付状态标签类型
    getPaymentStatusType(status) {
      const typeMap = {
        pending: "warning",
        paid: "success",
        failed: "danger",
        refunded: "info",
        null: "info",
        undefined: "info",
      };
      return typeMap[status] || "info";
    },
  },
};
</script>

<style lang="less" scoped>
.product-list {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  h1 {
    margin-bottom: 20px;
    font-size: 18px;
    color: #303133;
    font-weight: 600;
  }

  .search-bar {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    gap: 15px;

    .left-buttons {
      display: flex;
      gap: 10px;
    }

    .search-container {
      display: flex;
      gap: 10px;

      .input-group {
        margin-right: 0;

        .el-input {
          width: 180px;
        }
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
    padding-top: 15px;
    border-top: 1px solid #ebeef5;
  }

  .avatar-uploader {
    display: block;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 178px;
    height: 178px;

    &:hover {
      border-color: #409eff;
    }

    .avatar {
      width: 178px;
      height: 178px;
      display: block;
    }

    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 178px;
      height: 178px;
      line-height: 178px;
      text-align: center;
    }
  }

  .form-tip {
    margin-left: 10px;
    color: #909399;
    font-size: 12px;
  }

  .discount-amount {
    color: #f56c6c;
    font-weight: bold;
    font-size: 14px;
  }

  .unused-text {
    color: #909399;
    font-style: italic;
  }

  .order-id-link {
    color: #409eff;
    font-weight: 500;
    padding: 0;

    &:hover {
      color: #66b1ff;
    }
  }
}

/deep/ .el-table {
  margin-bottom: 15px;
}

/deep/ .el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
  padding: 8px 0;
}

/deep/ .el-table td {
  padding: 8px 0;
}

/deep/ .el-table--border,
.el-table--group {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

/deep/ .el-button--small {
  padding: 5px 12px;
}

/deep/ .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

/deep/ .el-dialog__body {
  padding: 20px;
}

/deep/ .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

/deep/ .el-form-item__label {
  font-weight: 500;
}

// 订单详情弹框样式
.order-detail-dialog {
  .detail-content {
    .order-info {
      margin-bottom: 20px;

      h4 {
        margin: 0 0 15px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
        border-bottom: 1px solid #ebeef5;
        padding-bottom: 8px;
      }

      .order-items {
        .order-item {
          display: flex;
          margin-bottom: 12px;
          align-items: center;

          .item-label {
            width: 100px;
            color: #606266;
            font-weight: 500;
            flex-shrink: 0;
          }

          .item-value {
            color: #303133;
            flex: 1;
          }
        }
      }
    }

    .order-total {
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 4px;
      margin-bottom: 20px;

      .total-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .total-label {
          color: #606266;
          font-weight: 500;
        }

        .total-value {
          color: #303133;
          font-weight: 600;
        }

        &.final-total {
          border-top: 1px solid #ebeef5;
          padding-top: 8px;
          margin-top: 8px;

          .total-label,
          .total-value {
            color: #f56c6c;
            font-size: 16px;
            font-weight: bold;
          }
        }
      }
    }

    .order-items-section {
      h4 {
        margin: 0 0 15px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
        border-bottom: 1px solid #ebeef5;
        padding-bottom: 8px;
      }
    }
  }
}
</style>
